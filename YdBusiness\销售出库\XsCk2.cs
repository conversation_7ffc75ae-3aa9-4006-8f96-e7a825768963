﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using BLL;
using Common.Delegate;
using Model;
using YdPublicFunction;
using Common;
using YdVar;

namespace YdBusiness
{
    public partial class XsCk2 : Common.BaseForm.DoubleFormRK1
    {
        private DataTable ZbTable;
        private DataRow ZbRow;
        public Common.Delegate.TransmitTxt ZbTransmitTxt = new TransmitTxt();
        private bool _frmInit = false;
        private BLL.BllYk_Ck1 _bllYkCk1 = new BllYk_Ck1();
        private BLL.BllYk_Ck2 _bllYkCk2 = new BllYk_Ck2();
        private Model.MdlYk_Ck1 _mdlYkCk1 = new MdlYk_Ck1();
        private decimal Ck_Money = 0;

        public XsCk2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            ZbRow = row;
            base.Insert = insert;
            ZbTable = table;
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void XsCk2_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            if (base.Insert)
                Zb_Clear();
            else
                Zb_Show();
            BtnState();
            _frmInit = true;
            Thread thread;
            thread = new Thread(this.Cb_Show);
            thread.IsBackground = true;
            thread.Start();
        }
        private void XsCk2_FormClosing(object sender, FormClosingEventArgs e)
        {

        }

        #region 自定义函数

        #region 初始化函数
        private void FormInit()
        {
            base.BaseBtnDelete = BtnDelete;
            base.BaseBtnNew = BtnNew;
            base.BaseBtnSave = BtnSave;
            base.BaseBtnClose = BtnClose;
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;

            BtnDelete.Location = new Point(30, 1);
            BtnDeleteAll.Location = new Point(BtnDelete.Left + BtnDelete.Width + 2, 1);
            BtnNew.Location = new Point(BtnDeleteAll.Left + BtnDeleteAll.Width + 2, 1);
            BtnSave.Location = new Point(BtnNew.Left + BtnNew.Width + 2, 1);
            BtnClose.Location = new Point(BtnSave.Left + BtnSave.Width + 2, 1);

            TxtCode.Enabled = false;
            NumCkMoney.Enabled = false;

            DtpDate.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.EditFormat = "yyyy-MM-dd HH:mm:ss";

            // 初始化药品批号下拉菜单
            comboYpPh1.Init();

            myGrid1.Init_Grid();
            myGrid1.Init_Column("明细ID", "Ck_ID", 80, "中", "", false);
            myGrid1.Init_Column("药品编码", "Yp_Code", 100, "中", "", false);
            myGrid1.Init_Column("药品名称", "Yp_Name", 200, "左", "", false);
            myGrid1.Init_Column("批准文号", "Yp_Pzwh", 200, "左", "", false);
            myGrid1.Init_Column("生产企业", "Yp_Scqy", 300, "左", "", false);
            myGrid1.Init_Column("规格", "Yp_Zjgg", 120, "左", "", false);
            myGrid1.Init_Column("单位", "Yp_Zjdw", 60, "中", "", false);
            myGrid1.Init_Column("剂型", "Yp_Jx", 60, "中", "", false);
            myGrid1.Init_Column("出库数量", "Ck_Sl", 100, "右", "#,##0.00", false);
            myGrid1.Init_Column("出库单价", "Ck_Dj", 100, "右", "#,##0.00", false);
            myGrid1.Init_Column("出库金额", "Ck_Money", 120, "右", "#,##0.00", false);
            myGrid1.Init_Column("备注", "Ck_Memo", 200, "左", "", false);
            myGrid1.AllowSort = true;
            myGrid1.AllowAddNew = true;
        }
        private void BtnState()
        {
            // 销售出库一般不需要完成状态控制，直接允许操作
            BtnDelete.Enabled = true;
            BtnDeleteAll.Enabled = true;
            BtnNew.Enabled = true;
            BtnSave.Enabled = true;
            BtnClose.Enabled = true;
            ControlEnable(true);
        }
        private void ControlEnable(bool flag)
        {
            TxtGkName.Enabled = flag;
            TxtGkTele.Enabled = flag;
            TxtGkSfzh.Enabled = flag;
            NumHyZk.Enabled = flag;
            NumJsMoney.Enabled = flag;
            DtpDate.Enabled = flag;
            TxtMemo.Enabled = flag;
            comboYpPh1.Enabled = flag;
            myGrid1.AllowAddNew = flag;
        }
        #endregion

        #region  显示函数
        protected override void Zb_Clear()
        {
            base.Insert = true;
            _mdlYkCk1 = new MdlYk_Ck1();
            ZbRow = ZbTable.NewRow();
            TxtCode.Text = _bllYkCk1.MaxCode(9);
            TxtGkName.Text = "";
            TxtGkTele.Text = "";
            TxtGkSfzh.Text = "";
            NumHyZk.Value = 1; // 默认折扣为1（无折扣）
            NumJsMoney.Value = 0;
            DtpDate.Value = DateTime.Now;
            NumCkMoney.Value = 0;
            TxtMemo.Text = "";
            comboYpPh1.SelectedIndex = -1;
            TxtGkName.Select();
        }

        private void Zb_Show()
        {
            _mdlYkCk1 = _bllYkCk1.GetModel(ZbRow["Ck_Code"] + "");
            TxtCode.Text = _mdlYkCk1.Ck_Code;
            TxtGkName.Text = _mdlYkCk1.Gk_Name;
            TxtGkTele.Text = _mdlYkCk1.Gk_Tele;
            TxtGkSfzh.Text = _mdlYkCk1.Gk_Sfzh;
            NumHyZk.Value = _mdlYkCk1.Hy_Zk ?? 1;
            NumJsMoney.Value = _mdlYkCk1.Js_Money ?? 0;
            DtpDate.Value = _mdlYkCk1.Ck_Date;
            NumCkMoney.Value = _mdlYkCk1.Ck_Money ?? 0;
            TxtMemo.Text = _mdlYkCk1.Ck_Memo;
            TxtGkName.Select();
        }

        private void Cb_Show()
        {
            MyTable = _bllYkCk2.GetList($"Ck_Code='{_mdlYkCk1.Ck_Code}'").Tables[0];
            MyTable.TableName = "明细";
            //主表记录
            MyCm = (CurrencyManager)BindingContext[MyTable];
            myGrid1.BeginInvoke(new Action<DataTable>(p =>
            {
                myGrid1.DataTable = p;
                this.LblTotal.Text = "∑=" + p.Rows.Count;
            }), MyTable);
            DataSum("");
        }
        #endregion

        #region 检查语句
        //检查主表数据
        private bool ZbCheck()
        {
            if (!this.Insert && _bllYkCk1.GetRecordCount("Ck_Code='" + TxtCode.Text + "'") == 0)
            {
                MessageBox.Show("此销售出库单已经被删除，无法继续操作!请点击新单，重新录入", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (CustomControl.Func.NotAllowEmpty(TxtGkName)) return false;
            if (DtpDate.Value == null)
            {
                MessageBox.Show("请选择出库日期!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                DtpDate.Focus();
                return false;
            }
            if ((DateTime)DtpDate.Value > Convert.ToDateTime("2079-06-01"))
            {
                DtpDate.Select();
                MessageBox.Show("填写的出库日期超出范围，请重新输入!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            return true;
        }
        private bool ZbStateCheck(string state)
        {
            if (state == "从表修改")
            {
                // 从表修改时的检查逻辑
            }
            if (state == "删除")
            {
                if (!this.Insert && _bllYkCk1.GetRecordCount("Ck_Code='" + TxtCode.Text + "'") == 0)
                {
                    MessageBox.Show("此销售出库单已经被删除，无法继续操作!请点击新单，重新录入", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            return true;
        }
        #endregion

        #region 按钮函数
        //删除行
        protected override bool DataDeleteOne()
        {
            if (myGrid1.Row >= myGrid1.RowCount)
            {
                MessageBox.Show("请选择一条记录!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            if (base.DataDeleteOne() == true)
            {
                _bllYkCk2.Delete(int.Parse(base.SubItemRow["Ck_ID"] + ""));
                MyTable.AcceptChanges();
                DataSum("");
                LblTotal.Text = "∑=" + (MyTable.Rows.Count).ToString();
                return true;
            }
            return false;
        }

        protected override bool DataDeleteAll(string PrimaryKey, Func<string, bool> Delete)
        {

            if (_mdlYkCk1 == null)
            {
                MessageBox.Show("数据尚未保存,无法删除!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            ZbRow.Delete();
            return base.DataDeleteAll(PrimaryKey, Delete);
        }

        protected override void DataNew()
        {
            myGrid1.UpdateData();
            if (base.MyTable.DataSet.HasChanges() == true)
            {
                if (MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.OK)
                {
                    DataSave(true);
                }
            }

            Zb_Clear();
            BtnState();
            Cb_Show();
            TxtGkName.Select();
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="showMsgbox">是否弹出提示框</param>
        /// <returns></returns>
        protected override bool DataSave(bool showMsgbox)
        {
            if (ZbCheck() == false) return false;
            if (base.Insert == true) TxtCode.Text = _bllYkCk1.MaxCode(9);
            DataSum("");
            if (base.Insert == true)
            {
                //增加记录
                Zb_Add();
            }
            else
            {
                //编辑记录
                Zb_Edit();
            }
            if (showMsgbox == true) MessageBox.Show("数据保存成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return true;
        }

        #region 数据操作函数

        protected override void DataSum(string s)
        {
            Ck_Money = MyTable.Compute("Sum(Ck_Money)", "") == DBNull.Value ? 0 : Convert.ToDecimal(MyTable.Compute("Sum(Ck_Money)", ""));
            myGrid1.Columns["Ck_Money"].FooterText = string.Format("{0:####0.00##}", Ck_Money);
            NumCkMoney.BeginInvoke(new Action<decimal>(p =>
            {
                NumCkMoney.Value = p;
                // 当出库金额变化时，自动计算实际付款金额
                if (_frmInit && Convert.ToDecimal(NumHyZk.Value) > 0)
                {
                    NumJsMoney.Value = p * Convert.ToDecimal(NumHyZk.Value);
                }
            }), Ck_Money);
            LblTotal.BeginInvoke(new Action(() => { LblTotal.Text = "∑=" + base.MyTable.Rows.Count.ToString(); }));
            if (!_mdlYkCk1.Ck_Code.IsNullOrEmpty())
            {
                _mdlYkCk1.Ck_Money = Ck_Money;
                _mdlYkCk1.Js_Money = Ck_Money * (Convert.ToDecimal(NumHyZk.Value) > 0 ? Convert.ToDecimal(NumHyZk.Value) : 1);
                _bllYkCk1.Update(_mdlYkCk1);
                ZbRow["Ck_Money"] = Ck_Money;
                ZbRow["Js_Money"] = _mdlYkCk1.Js_Money;
            }
        }

        #region 主表

        //增加记录
        private void Zb_Add()
        {
            _mdlYkCk1.Ck_Code = _bllYkCk1.MaxCode(9);
            TxtCode.Text = _mdlYkCk1.Ck_Code;
            _mdlYkCk1.Gk_Name = TxtGkName.Text.Trim();
            _mdlYkCk1.Gk_Tele = TxtGkTele.Text.Trim();
            _mdlYkCk1.Gk_Sfzh = TxtGkSfzh.Text.Trim();
            _mdlYkCk1.Hy_Zk = (decimal?)NumHyZk.Value;
            _mdlYkCk1.Js_Money = (decimal?)NumJsMoney.Value;
            _mdlYkCk1.Ck_Date = (DateTime)DtpDate.Value;
            _mdlYkCk1.Ck_Money = (decimal?)NumCkMoney.Value;
            _mdlYkCk1.Ck_Memo = TxtMemo.Text.Trim();
            _mdlYkCk1.Czy_Code = YdVar.Var.JsrCode;
            _mdlYkCk1.Czy_Name = YdVar.Var.UserName;
            _mdlYkCk1.Yd_Code = YdVar.Var.Yd_Code;
            _bllYkCk1.Add(_mdlYkCk1);
            Common.DataTableToList.ToDataRow<MdlYk_Ck1>(_mdlYkCk1, ZbRow);
            ZbTable.Rows.Add(ZbRow);
            base.Insert = false;
        }
        //编辑记录
        private void Zb_Edit()
        {
            _mdlYkCk1.Gk_Name = TxtGkName.Text.Trim();
            _mdlYkCk1.Gk_Tele = TxtGkTele.Text.Trim();
            _mdlYkCk1.Gk_Sfzh = TxtGkSfzh.Text.Trim();
            _mdlYkCk1.Hy_Zk = (decimal?)NumHyZk.Value;
            _mdlYkCk1.Js_Money = (decimal?)NumJsMoney.Value;
            _mdlYkCk1.Ck_Date = (DateTime)DtpDate.Value;
            _mdlYkCk1.Ck_Money = (decimal?)NumCkMoney.Value;
            _mdlYkCk1.Ck_Memo = TxtMemo.Text.Trim();
            _bllYkCk1.Update(_mdlYkCk1);
            Common.DataTableToList.ToDataRow<MdlYk_Ck1>(_mdlYkCk1, ZbRow);
        }
        #endregion

        #region 从表

        protected override void SubDataEdit()
        {
            if (ZbCheck() == false) return;
            if (ZbStateCheck("从表修改") == false) return;
            DataSave(false);
            bool subInsert;
            if ((myGrid1.Row + 1) > myGrid1.RowCount)
            {
                base.SubItemRow = base.MyTable.NewRow();
                subInsert = true;
            }
            else
            {
                base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
                subInsert = false;
            }

            // XsCk3 f = new XsCk3(_mdlYkCk1.Ck_Code, subInsert, base.SubItemRow, base.MyTable);
            // f.MyTransmitTxt = this.MyTransmitTxt;
            // f.Owner = this;
            // f.ShowDialog();

        }

        #endregion

        #endregion

        #endregion

        #endregion

        #region 控件动作

        #region 按钮动作

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DataDeleteOne();
        }
        private void BtnDeleteAll_Click(object sender, EventArgs e)
        {
            DataDeleteAll(_mdlYkCk1.Ck_Code, _bllYkCk1.Delete);
        }
        private void BtnNew_Click(object sender, EventArgs e)
        {
            DataNew();
        }
        private void BtnSave_Click(object sender, EventArgs e)
        {
            DataSave(true);
        }
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Grid动作
        private void myGrid1_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    this.SubDataEdit();
                    break;
            }
        }
        #endregion

        #region 数值控件事件
        private void NumHyZk_ValueChanged(object sender, EventArgs e)
        {
            // 当会员折扣变化时，自动计算实际付款金额
            if (_frmInit && Convert.ToDecimal(NumCkMoney.Value) > 0)
            {
                NumJsMoney.Value = Convert.ToDecimal(NumCkMoney.Value) * Convert.ToDecimal(NumHyZk.Value);
            }
        }
        #endregion

        #endregion


    }
}
