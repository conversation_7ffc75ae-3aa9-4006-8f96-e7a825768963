﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using BLL;
using Model;
using YdVar;
using Common.BaseForm;

namespace YdSysManage
{
    public partial class Sysconfig : BaseFather
    {
        private BLL.BllZd_Cs _bllZd_Cs = new BllZd_Cs();
        private Model.MdlZd_Cs _mdlZd_Cs = new MdlZd_Cs();
        private BLL.BllSysPara _bllSysPara = new BllSysPara();

        public Sysconfig()
        {
            InitializeComponent();
            this.Load += Sysconfig_Load;
        }

        private void Sysconfig_Load(object sender, EventArgs e)
        {
            InitializeControls();
            LoadData();
        }

        private void InitializeControls()
        {
            // 控件已在Designer中创建，这里可以做额外的初始化
        }

        private void LoadData()
        {
            try
            {
                // 获取当前药店的参数设置
                string ydCode = YdVar.Var.Yd_Code;
                _mdlZd_Cs = _bllZd_Cs.GetModel(ydCode);

                if (_mdlZd_Cs == null)
                {
                    // 如果没有记录，创建新的
                    _mdlZd_Cs = new MdlZd_Cs();
                    _mdlZd_Cs.Yd_Code = ydCode;
                }

                // 填充数据到控件
                FillDataToControls();
                LoadSysParaData();
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载数据失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FillDataToControls()
        {
            txtMsfxAppKey.Text = _mdlZd_Cs.appkey ?? "";
            txtMsfxAppSecret.Text = _mdlZd_Cs.secret ?? "";
            txtMsfxEntId.Text = _mdlZd_Cs.RefEntId ?? "";
        }



        private void BtnMsfxSave_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证数据
                if (!ValidateData())
                    return;

                // 获取控件数据
                GetDataFromControls();

                // 保存数据
                bool result;
                if (_bllZd_Cs.Exists(_mdlZd_Cs.Yd_Code))
                {
                    result = _bllZd_Cs.Update(_mdlZd_Cs);
                }
                else
                {
                    result = _bllZd_Cs.Add(_mdlZd_Cs);
                }

                if (result)
                {
                    MessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("保存失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnMsfxCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateData()
        {
            if (CustomControl.Func.NotAllowEmpty(txtMsfxAppKey)) return false;
            if (CustomControl.Func.NotAllowEmpty(txtMsfxAppSecret)) return false;
            if (CustomControl.Func.NotAllowEmpty(txtMsfxEntId)) return false;

            return true;
        }

        private void GetDataFromControls()
        {
            _mdlZd_Cs.Yd_Code = YdVar.Var.Yd_Code; // 药店编码从全局变量获取
            _mdlZd_Cs.appkey = txtMsfxAppKey.Text.Trim();
            _mdlZd_Cs.secret = txtMsfxAppSecret.Text.Trim();
            _mdlZd_Cs.RefEntId = txtMsfxEntId.Text.Trim();
        }

        private void LoadSysParaData()
        {
            try
            {
                // 加载临期预警天数参数
                Model.MdlSysPara mdlSysPara = _bllSysPara.GetModel("0001");
                if (mdlSysPara != null)
                {
                    numExpiryWarningDays.Value = decimal.Parse(mdlSysPara.ParaValue);
                }
                else
                {
                    numExpiryWarningDays.Value = 30; // 默认值
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载系统参数失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSysSave_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证数据
                decimal value = Convert.ToDecimal(numExpiryWarningDays.Value);
                if (value < 1 || value > 999)
                {
                    MessageBox.Show("临期预警天数必须在1-999之间！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 保存临期预警天数参数
                Model.MdlSysPara mdlSysPara = _bllSysPara.GetModel("0001");
                if (mdlSysPara == null)
                {
                    // 新增
                    mdlSysPara = new Model.MdlSysPara();
                    mdlSysPara.ParaCode = "0001";
                    mdlSysPara.ParaType = "系统参数";
                    mdlSysPara.ParaName = "临期预警天数";
                    mdlSysPara.ParaValue = Convert.ToDecimal(numExpiryWarningDays.Value).ToString("0");

                    if (_bllSysPara.Add(mdlSysPara))
                    {
                        MessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        // 重新加载PublicConfig中的配置
                        YdPara.PublicConfig.ReadConfig();
                    }
                    else
                    {
                        MessageBox.Show("保存失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    // 更新
                    mdlSysPara.ParaValue = Convert.ToDecimal(numExpiryWarningDays.Value).ToString("0");

                    if (_bllSysPara.Update(mdlSysPara))
                    {
                        MessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        // 重新加载PublicConfig中的配置
                        YdPara.PublicConfig.ReadConfig();
                    }
                    else
                    {
                        MessageBox.Show("保存失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSysCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
