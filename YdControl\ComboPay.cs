using System.Data;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace YdControl
{
    public partial class ComboPay : MyDtComobo
    {
        public ComboPay()
        {
            InitializeComponent();
        }

        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            BLL.BllZd_Pay _bllZd_Pay = new BllZd_Pay();
            this.DataView = _bllZd_Pay.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("Pay_Name", "支付方式名称", 150, "左");
            this.Init_Colum("Pay_Code", "编码", 60, "左");
            this.DisplayMember = "Pay_Name";
            this.ValueMember = "Pay_Code";
            int width = 250;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "Pay_Code+Pay_Name";
        }
    }
}
