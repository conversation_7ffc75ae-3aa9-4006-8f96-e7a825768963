using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Common.BaseForm;
using BLL;
using Model;
using YdPara;

namespace YdQuery
{
    public partial class Lq_Yj : BaseFather
    {
        private BllZd_Yp3 _bllZdYp3 = new BllZd_Yp3();
        private DataTable _dtExpiryWarning;

        public Lq_Yj()
        {
            InitializeComponent();
        }

        private void Lq_Yj_Load(object sender, EventArgs e)
        {
            FomInit();
        }

        private void FomInit()
        {
            // 初始化临期预警Grid
            myGrid1.Init_Grid();
            myGrid1.Init_Column("药品名称", "Yp_Name", 200, "左", "", false);
            myGrid1.Init_Column("简称", "Yp_Jc", 80, "左", "", false);
            myGrid1.Init_Column("生产企业", "Yp_Scqy", 300, "左", "", false);
            myGrid1.Init_Column("批准文号", "Yp_Pzwh", 150, "左", "", false);
            myGrid1.Init_Column("制剂规格", "Yp_Zjgg", 200, "左", "", false);
            myGrid1.Init_Column("制剂单位", "Yp_Zjdw", 80, "左", "", false);
            myGrid1.Init_Column("剂型", "Yp_Jx", 100, "左", "", false);
            myGrid1.Init_Column("包装规格", "Yp_Bzgg", 80, "左", "", false);
            myGrid1.Init_Column("包装数量", "Yp_Bzzhb", 80, "右", "#,##0", false);
            myGrid1.Init_Column("生产批号", "Yp_Scph", 120, "左", "", false);
            myGrid1.Init_Column("生产日期", "Yp_ScDate1", 100, "中", "yyyy-MM-dd", false);
            myGrid1.Init_Column("有效期至", "Yp_ScDate2", 100, "中", "yyyy-MM-dd", false);
            myGrid1.Init_Column("剩余天数", "RemainingDays", 80, "右", "#,##0", false);
            myGrid1.Init_Column("库存数量", "Yp_Count", 80, "右", "#,##0.##", false);
            myGrid1.AllowSort = true;
            myGrid1.ExtendRightColumn = false;
            // 自动加载临期预警数据
            LoadExpiryWarningData();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (_dtExpiryWarning != null)
                {
                    string filterText = TxtFilter.Text.Trim();
                    if (string.IsNullOrEmpty(filterText))
                    {
                        _dtExpiryWarning.DefaultView.RowFilter = "";
                    }
                    else
                    {
                        // 在药品名称、简称、批准文号中搜索
                        string filter = $"Yp_Name LIKE '%{filterText}%' OR Yp_Jc LIKE '%{filterText}%' OR Yp_Pzwh LIKE '%{filterText}%'";
                        _dtExpiryWarning.DefaultView.RowFilter = filter;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"过滤失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void LoadExpiryWarningData()
        {
            try
            {
                // 构建查询条件 - 只查询临期药品
                string whereCondition = $"DATEDIFF(day, GETDATE(), y3.Yp_ScDate2) <= {PublicConfig.ExpiryWarningDays} AND DATEDIFF(day, GETDATE(), y3.Yp_ScDate2) >= 0";

                _dtExpiryWarning = _bllZdYp3.GetListWithYpInfo(whereCondition).Tables[0];

                // 添加剩余天数计算列
                if (!_dtExpiryWarning.Columns.Contains("RemainingDays"))
                {
                    _dtExpiryWarning.Columns.Add("RemainingDays", typeof(int));
                }

                foreach (DataRow row in _dtExpiryWarning.Rows)
                {
                    if (row["Yp_ScDate2"] != DBNull.Value)
                    {
                        DateTime expiryDate = Convert.ToDateTime(row["Yp_ScDate2"]);
                        int remainingDays = (expiryDate - DateTime.Now).Days;
                        row["RemainingDays"] = remainingDays;
                    }
                }

                myGrid1.DataTable = _dtExpiryWarning;
                lblTotal.Text = $"共找到 {_dtExpiryWarning.Rows.Count} 条临期药品记录（预警天数：{PublicConfig.ExpiryWarningDays}天）";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载临期预警数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
}
