﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlYk_Ck2.cs
*
* 功 能： N/A
* 类 名： MdlYk_Ck2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-08-01 14:22:51   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// MdlYk_Ck2:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlYk_Ck2
	{
		public MdlYk_Ck2()
		{}
		#region Model
		private int _ck_id;
		private string _ck_code;
		private string _yp_code;
		private decimal? _ck_sl=0M;
		private decimal? _ck_dj=0M;
		private decimal? _ck_money=0M;
		private string _ck_memo;
		/// <summary>
		/// 
		/// </summary>
		public int Ck_Id
		{
			set{ _ck_id=value;}
			get{return _ck_id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ck_Code
		{
			set{ _ck_code=value;}
			get{return _ck_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Code
		{
			set{ _yp_code=value;}
			get{return _yp_code;}
		}
		/// <summary>
		/// 药房出库_数量
		/// </summary>
		public decimal? Ck_Sl
		{
			set{ _ck_sl=value;}
			get{return _ck_sl;}
		}
		/// <summary>
		/// 药房出库_单价
		/// </summary>
		public decimal? Ck_Dj
		{
			set{ _ck_dj=value;}
			get{return _ck_dj;}
		}
		/// <summary>
		/// 药房出库_金额
		/// </summary>
		public decimal? Ck_Money
		{
			set{ _ck_money=value;}
			get{return _ck_money;}
		}
		/// <summary>
		/// 药房出库_备注
		/// </summary>
		public string Ck_Memo
		{
			set{ _ck_memo=value;}
			get{return _ck_memo;}
		}
		#endregion Model

	}
}

