using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using YdPublicFunction;
using YdResources;

namespace YdBusiness
{
    public partial class XsCk1 : Common.BaseForm.BaseDict1
    {
        private BLL.BllYk_Ck1 _bllYkCk1 = new BllYk_Ck1();
        public XsCk1()
        {
            InitializeComponent();
        }

        private void XsCk1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            FormInit();
            DataInit();
            myGrid1.Select();
        }
        #region 初始化
        private void FormInit()
        {
            doubleDateEdit1.CustomFormat = "yyyy-MM-dd";
            doubleDateEdit1.DisplayFormat = "yyyy-MM-dd";
            doubleDateEdit1.EditFormat = "yyyy-MM-dd";
            doubleDateEdit1.SelectedIndex = 5;

            myGrid1.Init_Grid();
            myGrid1.Init_Column("出库编码", "Ck_Code", 120, "中", "", false);
            myGrid1.Init_Column("出库日期", "Ck_Date", 120, "中", "yyyy-MM-dd", false);
            myGrid1.Init_Column("顾客姓名", "Gk_Name", 150, "左", "", false);
            myGrid1.Init_Column("顾客电话", "Gk_Tele", 120, "左", "", false);
            myGrid1.Init_Column("出库金额", "Ck_Money", 120, "右", "#,##0.00", false);
            myGrid1.Init_Column("会员折扣", "Hy_Zk", 100, "右", "#,##0.00", false);
            myGrid1.Init_Column("结算金额", "Js_Money", 120, "右", "#,##0.00", false);
            myGrid1.Init_Column("操作员", "Czy_Name", 100, "左", "", false);
            myGrid1.Init_Column("支付方式", "Pay_Type", 100, "左", "", false);
            myGrid1.Init_Column("备注", "Ck_Memo", 200, "左", "", false);
            myGrid1.ColumnFooters = true;
            myGrid1.AllowSort = true;
        }
        #endregion

        #region 自定义函数
        private void DataInit()
        {
            base.MyTable = _bllYkCk1.GetList($"Ck_Date between '{DateTime.Parse(doubleDateEdit1.StartValue.ToString()).ToString("yyy-MM-dd")}' " +
                                         $"And '{DateTime.Parse(doubleDateEdit1.EndValue.ToString()).ToString("yyy-MM-dd 23:59:59")}'").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["Ck_Code"] };
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            myGrid1.BeginInvoke(new Action(() => this.myGrid1.DataTable = base.MyTable));
            base.MyView = (DataView)base.MyCm.List;
            base.MyView.Sort = "Ck_Date desc";
            DataSum();
        }
        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            XsCk2 vform = new XsCk2(base.Insert, base.MyRow, base.MyTable);
            vform.Tag = base.MyRow["Ck_Code"];
            vform.ZbTransmitTxt = base.MyTransmitTxt;
            base.AddTabControl(vform, "销售出库明细-" + (base.MyRow["Gk_Name"].ToString() == "" ? "新出库" : base.MyRow["Gk_Name"].ToString()), YdResources.C_Resources.GetImage16(""));
        }
        protected override void DataDelete()
        {
            if (myGrid1.Row + 1 > myGrid1.RowCount)
            {
                MessageBox.Show("请选择销售出库记录!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;

            if (MessageBox.Show("是否删除:销售出库【" + base.MyRow["Gk_Name"] + "】记录?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No) return;

            if (_bllYkCk1.Delete(base.MyRow["Ck_Code"].ToString()) == true)
            {
                myGrid1.Delete();
                base.MyTable.AcceptChanges();
                DataSum();
                MessageBox.Show("数据删除成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
        }

        private void DataSum()
        {
            LblTotal.BeginInvoke(new Action(() => this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString()));
        }
        #endregion

        #region 事件
        private void Cmd_Add_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataEdit(true);
        }
        private void Cmd_Del_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataDelete();
        }
        private void CmdQuery_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataInit();
        }
        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            char[] split = { ' ' };
            string filter = "Gk_Name+Ck_Code+Czy_Name+Pay_Type";
            string strFilter = "";
            foreach (string substr in TxtFilter.Text.Replace("*", "[*]").Replace("%", "[%]").Split(split))
            {
                strFilter = strFilter + filter + " like '*" + substr + "*' And ";
            }
            strFilter = strFilter.Substring(0, strFilter.Length - 5);
            MyView.RowFilter = strFilter;
            DataSum();
        }
        #endregion
    }
}
