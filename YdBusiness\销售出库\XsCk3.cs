using BLL;
using Model;
using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace YdBusiness
{
    public partial class XsCk3 : Common.BaseForm.DoubleFormRK2
    {
        private string ck_Code;
        private bool _frmInit = false;
        private BllYk_Ck2 _bllYkCk2 = new BllYk_Ck2();

        public XsCk3(string ck_Code, bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            this.ck_Code = ck_Code;
            base.Insert = insert;
            base.SubItemRow = row;
            base.MyTable = table;
            comboYpPh1.GotFocus += new System.EventHandler(base.InputEn);
        }

        private void XsCk3_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.SubItemRow);
            _frmInit = true;
        }

        #region 初始化函数
        private void FormInit()
        {
            TxtYpName.Enabled = false;
            TxtYpJx.Enabled = false;
            TxtYpZjgg.Enabled = false;
            TxtYpZjdw.Enabled = false;
            TxtYpScqy.Enabled = false;
            NumCkMoney.Enabled = false;
            // 初始化药品批号下拉菜单
            comboYpPh1.Init();

            // 设置按钮位置
            panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }
        #endregion

        #region 自定义函数
        private void DataClear()
        {
            base.Insert = true;
            comboYpPh1.Enabled = true;
            comboYpPh1.SelectedIndex = -1;

            TxtYpName.Text = "";
            TxtYpJx.Text = "";
            TxtYpZjgg.Text = "";
            TxtYpZjdw.Text = "";
            TxtYpScqy.Text = "";
            NumCkSl.Value = 0;
            NumCkDj.Value = 0;
            NumCkMoney.Value = 0;
            TxtCkMemo.Text = "";

            comboYpPh1.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;

            comboYpPh1.Enabled = false;
            comboYpPh1.SelectedValue = row["Yp_Code"] + "";

            NumCkSl.Value = decimal.Parse(row["Ck_Sl"] + "");
            NumCkDj.Value = decimal.Parse(row["Ck_Dj"] + "");
            NumCkMoney.Value = decimal.Parse(row["Ck_Money"] + "");
            TxtCkMemo.Text = row["Ck_Memo"] + "";

            NumCkSl.Select();
        }

        private bool DataCheck()
        {
            if (comboYpPh1.SelectedIndex < 0)
            {
                MessageBox.Show("请选择药品!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                comboYpPh1.Focus();
                return false;
            }
            if (NumCkSl.Value <= 0)
            {
                MessageBox.Show("出库数量必须大于0!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumCkSl.Focus();
                return false;
            }
            if (NumCkDj.Value <= 0)
            {
                MessageBox.Show("出库单价必须大于0!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumCkDj.Focus();
                return false;
            }
            return true;
        }

        private void DataAdd()
        {
            try
            {
                MdlYk_Ck2 mdlYkCk2 = new MdlYk_Ck2();
                mdlYkCk2.Ck_Code = ck_Code;
                mdlYkCk2.Yp_Code = comboYpPh1.SelectedValue + "";
                mdlYkCk2.Ck_Sl = NumCkSl.Value;
                mdlYkCk2.Ck_Dj = NumCkDj.Value;
                mdlYkCk2.Ck_Money = Common.MathFormula.Multiply(NumCkSl.Value, NumCkDj.Value);
                mdlYkCk2.Ck_Memo = TxtCkMemo.Text.Trim();

                int ck_id = _bllYkCk2.Add(mdlYkCk2);

                SubItemRow["Ck_ID"] = ck_id;
                SubItemRow["Ck_Code"] = ck_Code;
                SubItemRow["Yp_Code"] = comboYpPh1.SelectedValue + "";
                SubItemRow["Yp_Name"] = comboYpPh1.Text;
                SubItemRow["Yp_Pzwh"] = comboYpPh1.Columns["Yp_Pzwh"].Value + "";
                SubItemRow["Yp_Scqy"] = comboYpPh1.Columns["Yp_Scqy"].Value + "";
                SubItemRow["Yp_Zjgg"] = comboYpPh1.Columns["Yp_Zjgg"].Value + "";
                SubItemRow["Yp_Zjdw"] = comboYpPh1.Columns["Yp_Zjdw"].Value + "";
                SubItemRow["Yp_Jx"] = comboYpPh1.Columns["Yp_Jx"].Value + "";
                SubItemRow["Ck_Sl"] = NumCkSl.Value;
                SubItemRow["Ck_Dj"] = NumCkDj.Value;
                SubItemRow["Ck_Money"] = Common.MathFormula.Multiply(SubItemRow["Ck_Sl"], SubItemRow["Ck_Dj"]);
                SubItemRow["Ck_Memo"] = TxtCkMemo.Text;

                MyTable.Rows.Add(SubItemRow);
                MyTable.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("增加成功");
                comboYpPh1.Focus();
                DataClear();
            }
        }

        private void DataEdit()
        {
            MdlYk_Ck2 mdlYkCk2 = _bllYkCk2.GetModel(Convert.ToInt32(SubItemRow["Ck_ID"]));
            SubItemRow["Yp_Name"] = comboYpPh1.Text;
            SubItemRow["Ck_Code"] = ck_Code;
            SubItemRow["Yp_Code"] = comboYpPh1.SelectedValue + "";
            SubItemRow["Ck_Sl"] = NumCkSl.Value;
            SubItemRow["Ck_Dj"] = NumCkDj.Value;
            SubItemRow["Ck_Money"] = Common.MathFormula.Multiply(SubItemRow["Ck_Sl"], SubItemRow["Ck_Dj"]);
            SubItemRow["Ck_Memo"] = TxtCkMemo.Text;
            SubItemRow["Yp_Code"] = comboYpPh1.SelectedValue + "";

            mdlYkCk2.Ck_Code = ck_Code;
            mdlYkCk2.Yp_Code = comboYpPh1.SelectedValue + "";
            mdlYkCk2.Ck_Sl = NumCkSl.Value;
            mdlYkCk2.Ck_Dj = NumCkDj.Value;
            mdlYkCk2.Ck_Money = Common.MathFormula.Multiply(NumCkSl.Value, NumCkDj.Value);
            mdlYkCk2.Ck_Memo = TxtCkMemo.Text.Trim();

            try
            {
                _bllYkCk2.Update(mdlYkCk2);
                MyTable.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                comboYpPh1.Focus();
            }
        }

        private void ComputeMoney()
        {
            NumCkMoney.Value = Common.MathFormula.Multiply(NumCkSl.Value, NumCkDj.Value);
        }


        #endregion

        #region 事件处理
        private void NumCkSl_ValueChanged(object sender, EventArgs e)
        {
            ComputeMoney();
        }

        private void NumCkDj_ValueChanged(object sender, EventArgs e)
        {
            ComputeMoney();
        }

        private void comboYpPh1_RowChange(object sender, EventArgs e)
        {
            if (comboYpPh1.WillChangeToValue == null)
            {
                TxtYpName.Text = "";
                TxtYpJx.Text = "";
                TxtYpZjgg.Text = "";
                TxtYpZjdw.Text = "";
                TxtYpScqy.Text = "";
            }
            else
            {
                TxtYpName.Text = comboYpPh1.Columns["Yp_Name"].Value + "";
                TxtYpJx.Text = comboYpPh1.Columns["Yp_Jx"].Value + "";
                TxtYpZjgg.Text = comboYpPh1.Columns["Yp_Zjgg"].Value + "";
                TxtYpZjdw.Text = comboYpPh1.Columns["Yp_Zjdw"].Value + "";
                TxtYpScqy.Text = comboYpPh1.Columns["Yp_Scqy"].Value + "";
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck())
            {
                if (base.Insert)
                {
                    DataAdd();
                }
                else
                {
                    DataEdit();
                }
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion
    }
}
