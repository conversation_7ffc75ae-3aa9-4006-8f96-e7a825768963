using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using BLL;
using Common.Delegate;
using Model;
using YdPublicFunction;
using Common;
using YdVar;

namespace YdBusiness
{
    public partial class Dd_Order2 : Common.BaseForm.DoubleFormRK1
    {
        private DataTable ZbTable;
        private DataRow ZbRow;
        public Common.Delegate.TransmitTxt ZbTransmitTxt = new TransmitTxt();
        private bool _frmInit = false;
        private BLL.BllDd1 _bllDd1 = new BllDd1();
        private BLL.BllDd2 _bllDd2 = new BllDd2();
        private Model.MdlDd1 _mdlDd1 = new MdlDd1();
        private decimal Dd_Money = 0;

        public Dd_Order2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            ZbRow = row;
            base.Insert = insert;
            ZbTable = table;
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void Dd_Order2_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            if (base.Insert)
                Zb_Clear();
            else
                Zb_Show();
            BtnState();
            _frmInit = true;
            Thread thread;
            thread = new Thread(this.Cb_Show);
            thread.IsBackground = true;
            thread.Start();
        }
        private void Dd_Order2_FormClosing(object sender, FormClosingEventArgs e)
        {

        }

        #region 自定义函数

        #region 初始化函数
        private void FormInit()
        {
            base.BaseBtnDelete = BtnDelete;
            base.BaseBtnNew = BtnNew;
            base.BaseBtnSave = BtnSave;
            base.BaseBtnClose = BtnClose;
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;

            BtnDelete.Location = new Point(30, 1);
            BtnDeleteAll.Location = new Point(BtnDelete.Left + BtnDelete.Width + 2, 1);
            BtnNew.Location = new Point(BtnDeleteAll.Left + BtnDeleteAll.Width + 2, 1);
            BtnSave.Location = new Point(BtnNew.Left + BtnNew.Width + 2, 1);
            BtnClose.Location = new Point(BtnSave.Left + BtnSave.Width + 2, 1);

            TxtCode.Enabled = false;
            NumDdMoney.Enabled = false;

            DtpDate.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.EditFormat = "yyyy-MM-dd HH:mm:ss";

            // 初始化供应商下拉菜单
            comboGys1.Init();

            myGrid1.Init_Grid();
            myGrid1.Init_Column("明细ID", "Dd_ID", 80, "中", "", false);
            myGrid1.Init_Column("药品编码", "Xl_Code", 100, "中", "", false);
            myGrid1.Init_Column("药品名称", "Yp_Name", 200, "左", "", false);
            myGrid1.Init_Column("批准文号", "Yp_Pzwh", 200, "左", "", false);
            myGrid1.Init_Column("生产企业", "Yp_Scqy", 300, "左", "", false);
            myGrid1.Init_Column("规格", "Yp_Zjgg", 120, "左", "", false);
            myGrid1.Init_Column("单位", "Yp_Zjdw", 60, "中", "", false);
            myGrid1.Init_Column("剂型", "Yp_Jx", 60, "中", "", false);
            myGrid1.Init_Column("订单数量", "Dd_Sl", 100, "右", "#,##0.00", false);
            myGrid1.Init_Column("订单单价", "Dd_Dj", 100, "右", "#,##0.00", false);
            myGrid1.Init_Column("订单金额", "Dd_Money", 120, "右", "#,##0.00", false);
            myGrid1.Init_Column("备注", "Dd_Memo", 200, "左", "", false);
            myGrid1.AllowSort = true;
            myGrid1.AllowAddNew = true;
        }
        private void BtnState()
        {
            if (_mdlDd1.Dd_Finish == "0")
            {
                BtnDelete.Enabled = true;
                BtnDeleteAll.Enabled = true;
                BtnNew.Enabled = true;
                BtnSave.Enabled = true;
                BtnClose.Enabled = true;
                ControlEnable(true);
            }
            else
            {
                BtnDelete.Enabled = false;
                BtnDeleteAll.Enabled = false;
                BtnNew.Enabled = true;
                BtnSave.Enabled = false;
                BtnClose.Enabled = true;
                ControlEnable(false);
            }
        }
        private void ControlEnable(bool flag)
        {
            comboGys1.Enabled = flag;
            DtpDate.Enabled = flag;
            TxtMemo.Enabled = flag;
            myGrid1.AllowAddNew = flag;
        }
        #endregion

        #region  显示函数
        protected override void Zb_Clear()
        {
            base.Insert = true;
            _mdlDd1 = new MdlDd1();
            ZbRow = ZbTable.NewRow();
            TxtCode.Text = _bllDd1.MaxCode(9);
            comboGys1.SelectedIndex = -1;
            DtpDate.Value = DateTime.Now;
            NumDdMoney.Value = 0;
            TxtMemo.Text = "";
            ZbPictureShow("");
            comboGys1.Select();
        }

        private void Zb_Show()
        {
            _mdlDd1 = _bllDd1.GetModel(ZbRow["Dd_Code"] + "");
            TxtCode.Text = _mdlDd1.Dd_Code;
            comboGys1.SelectedValue = _mdlDd1.Kh_Code;
            DtpDate.Value = _mdlDd1.Dd_Date ?? DateTime.Now;
            NumDdMoney.Value = _mdlDd1.Dd_Money ?? 0;
            TxtMemo.Text = _mdlDd1.Dd_Memo;
            ZbPictureShow(_mdlDd1.Dd_Finish);
            comboGys1.Select();
        }
        private void ZbPictureShow(string Dd_Finish)
        {
            if (Dd_Finish == "1")
            {
                pictureBox1.Image = YdResources.StateRes.已完成;
            }
            if (Dd_Finish == "0")
            {
                pictureBox1.Image = YdResources.StateRes.未完成;
            }
            if (Dd_Finish == "")
            {
                pictureBox1.Image = YdResources.StateRes.新单;
            }

        }
        private void Cb_Show()
        {
            MyTable = _bllDd2.GetList($"Dd_Code='{_mdlDd1.Dd_Code}'").Tables[0];
            MyTable.TableName = "明细";
            //主表记录
            MyCm = (CurrencyManager)BindingContext[MyTable];
            myGrid1.BeginInvoke(new Action<DataTable>(p =>
            {
                myGrid1.DataTable = p;
                this.LblTotal.Text = "∑=" + p.Rows.Count;
            }), MyTable);
            DataSum("");
        }
        #endregion

        #region 检查语句
        //检查主表数据
        private bool ZbCheck()
        {
            if (!this.Insert && _bllDd1.GetRecordCount("Dd_Code='" + TxtCode.Text + "'") == 0)
            {
                MessageBox.Show("此采购订单已经被删除，无法继续操作!请点击新单，重新录入", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (comboGys1.SelectedIndex < 0)
            {
                MessageBox.Show("请选择供应商!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                comboGys1.Focus();
                return false;
            }
            if (DtpDate.Value == null)
            {
                MessageBox.Show("请选择订单日期!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                DtpDate.Focus();
                return false;
            }
            if ((DateTime)DtpDate.Value > Convert.ToDateTime("2079-06-01"))
            {
                DtpDate.Select();
                MessageBox.Show("填写的订单日期超出范围，请重新输入!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            return true;
        }
        private bool ZbStateCheck(string state)
        {
            if (state == "从表修改")
            {
                // 从表修改时的检查逻辑
            }
            if (state == "删除")
            {
                if (!this.Insert && _bllDd1.GetRecordCount("Dd_Code='" + TxtCode.Text + "'") == 0)
                {
                    MessageBox.Show("此采购订单已经被删除，无法继续操作!请点击新单，重新录入", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            return true;
        }
        #endregion

        #region 按钮函数
        //删除行
        protected override bool DataDeleteOne()
        {
            if (myGrid1.Row >= myGrid1.RowCount)
            {
                MessageBox.Show("请选择一条记录!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            if (base.DataDeleteOne() == true)
            {
                _bllDd2.Delete(int.Parse(base.SubItemRow["Dd_ID"] + ""));
                MyTable.AcceptChanges();
                DataSum("");
                LblTotal.Text = "∑=" + (MyTable.Rows.Count).ToString();
                return true;
            }
            return false;
        }

        protected override bool DataDeleteAll(string PrimaryKey, Func<string, bool> Delete)
        {

            if (_mdlDd1 == null)
            {
                MessageBox.Show("数据尚未保存,无法删除!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            ZbRow.Delete();
            return base.DataDeleteAll(PrimaryKey, Delete);
        }

        protected override void DataNew()
        {
            myGrid1.UpdateData();
            if (base.MyTable.DataSet.HasChanges() == true)
            {
                if (MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.OK)
                {
                    DataSave(true);
                }
            }

            Zb_Clear();
            BtnState();
            Cb_Show();
            comboGys1.Select();
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="showMsgbox">是否弹出提示框</param>
        /// <returns></returns>
        protected override bool DataSave(bool showMsgbox)
        {
            if (ZbCheck() == false) return false;
            if (base.Insert == true) TxtCode.Text = _bllDd1.MaxCode(9);
            DataSum("");
            if (base.Insert == true)
            {
                //增加记录
                Zb_Add();
            }
            else
            {
                //编辑记录
                Zb_Edit();
            }
            if (showMsgbox == true) MessageBox.Show("数据保存成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return true;
        }

        #region 数据操作函数

        protected override void DataSum(string s)
        {
            Dd_Money = MyTable.Compute("Sum(Dd_Money)", "") == DBNull.Value ? 0 : Convert.ToDecimal(MyTable.Compute("Sum(Dd_Money)", ""));
            myGrid1.Columns["Dd_Money"].FooterText = string.Format("{0:####0.00##}", Dd_Money);
            NumDdMoney.BeginInvoke(new Action<decimal>(p => { NumDdMoney.Value = p; }), Dd_Money);
            LblTotal.BeginInvoke(new Action(() => { LblTotal.Text = "∑=" + base.MyTable.Rows.Count.ToString(); }));
            if (!_mdlDd1.Dd_Code.IsNullOrEmpty())
            {
                _mdlDd1.Dd_Money = Dd_Money;
                _bllDd1.Update(_mdlDd1);
                ZbRow["Dd_Money"] = Dd_Money;
            }
        }

        #region 主表

        //增加记录
        private void Zb_Add()
        {
            _mdlDd1.Dd_Code = _bllDd1.MaxCode(9);
            TxtCode.Text = _mdlDd1.Dd_Code;
            _mdlDd1.Kh_Code = comboGys1.SelectedValue.ToString();
            _mdlDd1.Kh_Name = comboGys1.Text;
            _mdlDd1.Dd_Date = (DateTime)DtpDate.Value;
            _mdlDd1.Dd_Money = (decimal?)NumDdMoney.Value;
            _mdlDd1.Dd_Memo = TxtMemo.Text.Trim();
            _mdlDd1.Dd_Finish = "0";
            _mdlDd1.Cgy_Code = YdVar.Var.JsrCode;
            _mdlDd1.Cgy_Name = YdVar.Var.UserName;
            _bllDd1.Add(_mdlDd1);
            Common.DataTableToList.ToDataRow<MdlDd1>(_mdlDd1, ZbRow);
            ZbTable.Rows.Add(ZbRow);
            base.Insert = false;
        }
        //编辑记录
        private void Zb_Edit()
        {
            _mdlDd1.Kh_Code = comboGys1.SelectedValue.ToString();
            _mdlDd1.Kh_Name = comboGys1.Text;
            _mdlDd1.Dd_Date = (DateTime)DtpDate.Value;
            _mdlDd1.Dd_Money = (decimal?)NumDdMoney.Value;
            _mdlDd1.Dd_Memo = TxtMemo.Text.Trim();
            _bllDd1.Update(_mdlDd1);
            Common.DataTableToList.ToDataRow<MdlDd1>(_mdlDd1, ZbRow);
        }
        #endregion

        #region 从表

        protected override void SubDataEdit()
        {
            if (ZbCheck() == false) return;
            if (ZbStateCheck("从表修改") == false) return;
            DataSave(false);
            bool subInsert;
            if ((myGrid1.Row + 1) > myGrid1.RowCount)
            {
                base.SubItemRow = base.MyTable.NewRow();
                subInsert = true;
            }
            else
            {
                base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
                subInsert = false;
            }

            Dd_Order3 f = new Dd_Order3(_mdlDd1.Dd_Code, subInsert, base.SubItemRow, base.MyTable);
            f.MyTransmitTxt = this.MyTransmitTxt;
            f.Owner = this;
            f.ShowDialog();

        }

        #endregion

        #endregion

        #endregion

        #endregion

        #region 控件动作

        #region 按钮动作

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DataDeleteOne();
        }
        private void BtnDeleteAll_Click(object sender, EventArgs e)
        {
            DataDeleteAll(_mdlDd1.Dd_Code, _bllDd1.Delete);
        }
        private void BtnNew_Click(object sender, EventArgs e)
        {
            DataNew();
        }
        private void BtnSave_Click(object sender, EventArgs e)
        {
            DataSave(true);
        }
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Grid动作
        private void myGrid1_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    this.SubDataEdit();
                    break;
            }
        }
        #endregion

        #endregion


    }
}
