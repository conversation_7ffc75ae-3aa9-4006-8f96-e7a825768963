﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Security.AccessControl;
using System.Text;
using BLL;
using Model;

namespace YdPara
{
    public class PublicConfig
    {
        /// <summary>
        /// 临期预警天数
        /// </summary>
        public static int ExpiryWarningDays { get; set; } = 30;

        public static void ReadConfig()
        {
            BLL.BllSysPara bllSysPara = new BllSysPara();
            foreach (Model.MdlSysPara mdlSysPara in bllSysPara.GetModelList(""))
            {
                switch (mdlSysPara.ParaCode)
                {
                    case "0001":
                        ExpiryWarningDays = int.Parse(mdlSysPara.ParaValue);
                        break;
                }
            }
        }

    }
}
