﻿/**  版本信息模板在安装目录下，可自行修改。
* DalYk_CkDrugtracinfo.cs
*
* 功 能： N/A
* 类 名： DalYk_CkDrugtracinfo
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-08-01 14:22:52   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalYk_CkDrugtracinfo
	/// </summary>
	public partial class DalYk_CkDrugtracinfo:IDalYk_CkDrugtracinfo
	{
		public DalYk_CkDrugtracinfo()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return Common.WinFormVar.Var.DbHelper.GetMaxID("Ck_Id", "Yk_CkDrugtracinfo"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int Ck_Id,string drug_trac_codg)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Yk_CkDrugtracinfo");
			strSql.Append(" where Ck_Id=@Ck_Id and drug_trac_codg=@drug_trac_codg ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Id", SqlDbType.Int,4),
					new SqlParameter("@drug_trac_codg", SqlDbType.VarChar,100)			};
			parameters[0].Value = Ck_Id;
			parameters[1].Value = drug_trac_codg;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlYk_CkDrugtracinfo model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Yk_CkDrugtracinfo(");
			strSql.Append("Ck_Id,Ck_Code,drug_trac_codg)");
			strSql.Append(" values (");
			strSql.Append("@Ck_Id,@Ck_Code,@drug_trac_codg)");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Id", SqlDbType.Int,4),
					new SqlParameter("@Ck_Code", SqlDbType.Char,9),
					new SqlParameter("@drug_trac_codg", SqlDbType.VarChar,100)};
			parameters[0].Value = model.Ck_Id;
			parameters[1].Value = model.Ck_Code;
			parameters[2].Value = model.drug_trac_codg;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlYk_CkDrugtracinfo model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Yk_CkDrugtracinfo set ");
			strSql.Append("Ck_Code=@Ck_Code");
			strSql.Append(" where Ck_Id=@Ck_Id and drug_trac_codg=@drug_trac_codg ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Code", SqlDbType.Char,9),
					new SqlParameter("@Ck_Id", SqlDbType.Int,4),
					new SqlParameter("@drug_trac_codg", SqlDbType.VarChar,100)};
			parameters[0].Value = model.Ck_Code;
			parameters[1].Value = model.Ck_Id;
			parameters[2].Value = model.drug_trac_codg;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int Ck_Id,string drug_trac_codg)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Yk_CkDrugtracinfo ");
			strSql.Append(" where Ck_Id=@Ck_Id and drug_trac_codg=@drug_trac_codg ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Id", SqlDbType.Int,4),
					new SqlParameter("@drug_trac_codg", SqlDbType.VarChar,100)			};
			parameters[0].Value = Ck_Id;
			parameters[1].Value = drug_trac_codg;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlYk_CkDrugtracinfo GetModel(int Ck_Id,string drug_trac_codg)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Ck_Id,Ck_Code,drug_trac_codg from Yk_CkDrugtracinfo ");
			strSql.Append(" where Ck_Id=@Ck_Id and drug_trac_codg=@drug_trac_codg ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ck_Id", SqlDbType.Int,4),
					new SqlParameter("@drug_trac_codg", SqlDbType.VarChar,100)			};
			parameters[0].Value = Ck_Id;
			parameters[1].Value = drug_trac_codg;

			Model.MdlYk_CkDrugtracinfo model=new Model.MdlYk_CkDrugtracinfo();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlYk_CkDrugtracinfo DataRowToModel(DataRow row)
		{
			Model.MdlYk_CkDrugtracinfo model=new Model.MdlYk_CkDrugtracinfo();
			if (row != null)
			{
				if(row["Ck_Id"]!=null && row["Ck_Id"].ToString()!="")
				{
					model.Ck_Id=int.Parse(row["Ck_Id"].ToString());
				}
				if(row["Ck_Code"]!=null)
				{
					model.Ck_Code=row["Ck_Code"].ToString();
				}
				if(row["drug_trac_codg"]!=null)
				{
					model.drug_trac_codg=row["drug_trac_codg"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Ck_Id,Ck_Code,drug_trac_codg ");
			strSql.Append(" FROM Yk_CkDrugtracinfo ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Ck_Id,Ck_Code,drug_trac_codg ");
			strSql.Append(" FROM Yk_CkDrugtracinfo ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Yk_CkDrugtracinfo ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.drug_trac_codg desc");
			}
			strSql.Append(")AS Row, T.*  from Yk_CkDrugtracinfo T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Yk_CkDrugtracinfo";
			parameters[1].Value = "drug_trac_codg";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

